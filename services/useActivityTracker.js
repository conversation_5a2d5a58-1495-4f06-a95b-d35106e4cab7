import { useEffect } from "react";
import { AppState } from "react-native";
import { useAuthStore } from "./authStore";

/**
 * Hook to track user activity and update last activity timestamp
 */
export const useActivityTracker = () => {
  const { updateLastActivity, isAuthenticated } = useAuthStore();

  useEffect(() => {
    if (!isAuthenticated) return;

    // Update activity when app becomes active
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === "active") {
        updateLastActivity();
      }
    };

    // Initial activity update
    updateLastActivity();

    // Listen for app state changes
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    // Update activity every 5 minutes while app is active
    const activityInterval = setInterval(() => {
      if (AppState.currentState === "active") {
        updateLastActivity();
      }
    }, 20 * 60 * 1000); // 5 minutes

    return () => {
      subscription?.remove();
      clearInterval(activityInterval);
    };
  }, [isAuthenticated, updateLastActivity]);
};

export default useActivityTracker;
