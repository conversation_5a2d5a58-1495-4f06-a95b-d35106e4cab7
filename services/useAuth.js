import { useState } from "react";
import { authApi, authStorage } from "./api";
import { useAuthStore } from "./authStore";

/**
 * Custom hook for authentication operations
 * Provides login, logout, and 2FA validation functionality
 */
export const useAuthOperations = () => {
  const { setAuthData, setSessionID, clearAuthData, setError } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Login user with username and password
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.user - Username
   * @param {string} credentials.password - Password
   * @returns {Promise<Object>} Login response
   */
  const login = async (credentials) => {
    setIsLoading(true);
    try {
      const response = await authApi.login(credentials);

      if (response.sessionID) {
        // User has 2FA enabled
        await setSessionID(response.sessionID);
        return { requiresTwoFA: true, sessionID: response.sessionID };
      } else if (response.token) {
        // Direct login without 2FA
        await setAuthData({
          token: response.token,
          user: response.user,
          role: response.role,
        });
        return { requiresTwoFA: false, authData: response };
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      setError(error.message || "Login failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Validate 2FA code
   * @param {string} sessionID - Session ID from login
   * @param {string} code - 2FA code from authenticator
   * @returns {Promise<Object>} Validation response
   */
  const validate2FA = async (sessionID, code) => {
    setIsLoading(true);
    try {
      const response = await authApi.validate2fa({ sessionID, code });

      if (response.token) {
        await setAuthData({
          token: response.token,
          user: response.user,
          role: response.role,
        });
        // Clear session ID as it's no longer needed
        await authStorage.clearSessionID();
        return response;
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      setError(error.message || "2FA validation failed");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logout user and clear all auth data
   */
  const logout = async () => {
    setIsLoading(true);
    try {
      // Clear all authentication data
      await clearAuthData();

      console.log("User logged out successfully");
    } catch (error) {
      console.error("Logout error:", error);
      // Still clear local data even if API call fails
      await clearAuthData();
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Check server status
   * @returns {Promise<Object>} Server status
   */
  const checkServerStatus = async () => {
    try {
      return await authApi.getServerStatus();
    } catch (error) {
      console.error("Server status check failed:", error);
      throw error;
    }
  };

  return {
    login,
    validate2FA,
    logout,
    checkServerStatus,
    isLoading,
  };
};

export default useAuthOperations;
