import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";

const STORAGE_KEYS = {
  SETTINGS: "nimbl-setting",
  AUTH: "nimbl-auth",
};

const DEFAULT_BASE_URL = "http://192.168.31.147:27182";
const API_VERSION = "v1";

/**
 * Get base URL from AsyncStorage or return default
 * @returns {Promise<string>} Base URL for API calls
 */
const getBaseURL = async () => {
  try {
    const settings = await AsyncStorage.getItem(STORAGE_KEYS.SETTINGS);
    return settings ? JSON.parse(settings).baseURL : DEFAULT_BASE_URL;
  } catch (error) {
    console.error("Error getting base URL:", error);
    return DEFAULT_BASE_URL;
  }
};

/**
 * Get auth token from AsyncStorage
 * @returns {Promise<string>} Authentication token
 */
const getAuthToken = async () => {
  try {
    const auth = await AsyncStorage.getItem(STORAGE_KEYS.AUTH);
    return auth ? JSON.parse(auth).token : "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
};

/**
 * Create axios instance with dynamic base URL
 */
const createAxiosInstance = async () => {
  const baseURL = await getBaseURL();
  const token = await getAuthToken();

  const instance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add auth token if available
  if (token) {
    instance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  }

  // Request interceptor to add token dynamically
  instance.interceptors.request.use(
    async (config) => {
      const currentToken = await getAuthToken();
      if (currentToken) {
        config.headers.Authorization = `Bearer ${currentToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        // Handle unauthorized access
        AsyncStorage.removeItem(STORAGE_KEYS.AUTH);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

/**
 * Handle API errors consistently
 * @param {Error} error - Axios error object
 */
const handleApiError = (error) => {
  if (error.response) {
    const message =
      error.response.data?.message ||
      error.response.data ||
      "An error occurred";
    throw new Error(message);
  } else if (error.request) {
    throw new Error("Network error - please check your connection");
  } else {
    throw new Error(error.message || "An unexpected error occurred");
  }
};

/**
 * API endpoints
 */
const endpoints = {
  status: "api",
  login: `api/${API_VERSION}/login`,
  validate2fa: `api/${API_VERSION}/2fa/validate`,
  users: `api/${API_VERSION}/users`,
  devices: `api/${API_VERSION}/devices`,
  topology: `api/${API_VERSION}/topology`,
  subnetGroups: `api/${API_VERSION}/subnet-groups`,
};

/**
 * Authentication API methods
 */
export const authApi = {
  /**
   * Login to the system
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.user - Username
   * @param {string} credentials.password - Password
   * @returns {Promise<Object>} Login response
   */
  login: async (credentials) => {
    try {
      const axiosInstance = await createAxiosInstance();
      const { data } = await axiosInstance.post(endpoints.login, credentials);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Validate 2FA code
   * @param {Object} data2fa - 2FA validation data
   * @param {string} data2fa.sessionID - Session ID from login
   * @param {string} data2fa.code - 2FA code from authenticator
   * @returns {Promise<Object>} Validation response with token
   */
  validate2fa: async (data2fa) => {
    try {
      const axiosInstance = await createAxiosInstance();
      const { data } = await axiosInstance.post(endpoints.validate2fa, data2fa);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get server status
   * @returns {Promise<Object>} Server status
   */
  getServerStatus: async () => {
    try {
      const axiosInstance = await createAxiosInstance();
      const { data } = await axiosInstance.get(endpoints.status);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get subnet groups
   * @returns {Promise<Array>} List of subnet groups
   */
  getSubnetGroups: async () => {
    try {
      const axiosInstance = await createAxiosInstance();
      const { data } = await axiosInstance.get(endpoints.subnetGroups);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get devices by group ID
   * @param {string} groupId - Group ID to filter devices
   * @returns {Promise<Array>} List of devices in the group
   */
  getDevicesByGroup: async (groupId) => {
    try {
      const axiosInstance = await createAxiosInstance();
      const { data } = await axiosInstance.get(
        `${endpoints.devices}?groupid=${groupId}`
      );
      const getDevicesByGroup = Object.values(data) || [];
      //filter out devices with with mac address 11-22-33-44-55-66
      const filteredDevices = getDevicesByGroup.filter(
        (device) => device.mac !== "11-22-33-44-55-66"
      );
      return filteredDevices;
    } catch (error) {
      handleApiError(error);
    }
  },
};

/**
 * Authentication storage utilities
 */
export const authStorage = {
  /**
   * Store authentication data
   * @param {Object} authData - Authentication data
   */
  setAuthData: async (authData) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH, JSON.stringify(authData));
    } catch (error) {
      console.error("Error storing auth data:", error);
    }
  },

  /**
   * Get authentication data
   * @returns {Promise<Object|null>} Authentication data
   */
  getAuthData: async () => {
    try {
      const authData = await AsyncStorage.getItem(STORAGE_KEYS.AUTH);
      return authData ? JSON.parse(authData) : null;
    } catch (error) {
      console.error("Error getting auth data:", error);
      return null;
    }
  },

  /**
   * Clear authentication data
   */
  clearAuthData: async () => {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AUTH);
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
  },

  /**
   * Store session ID for 2FA
   * @param {string} sessionID - Session ID
   */
  setSessionID: async (sessionID) => {
    try {
      await AsyncStorage.setItem("sessionID", sessionID);
    } catch (error) {
      console.error("Error storing session ID:", error);
    }
  },

  /**
   * Get session ID
   * @returns {Promise<string|null>} Session ID
   */
  getSessionID: async () => {
    try {
      return await AsyncStorage.getItem("sessionID");
    } catch (error) {
      console.error("Error getting session ID:", error);
      return null;
    }
  },

  /**
   * Clear session ID
   */
  clearSessionID: async () => {
    try {
      await AsyncStorage.removeItem("sessionID");
    } catch (error) {
      console.error("Error clearing session ID:", error);
    }
  },
};
