import React from "react";
import { create } from "zustand";
import { authStorage } from "./api";

// Initial state
const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  role: null,
  sessionID: null,
  loginTime: null,
  lastActivity: null,
  isLoading: true,
  error: null,
};

/**
 * Zustand store for authentication state management
 */
export const useAuthStore = create((set) => ({
  ...initialState,

  // Actions
  setLoading: (isLoading) => set({ isLoading }),

  setAuthData: async (authData) => {
    try {
      // Enhanced auth data with additional user details
      const enhancedAuthData = {
        ...authData,
        loginTime: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
      };

      await authStorage.setAuthData(enhancedAuthData);
      set({
        isAuthenticated: true,
        user: authData.user || authData.username,
        token: authData.token,
        role: authData.role || "user",
        sessionID: authData.sessionID || null,
        loginTime: enhancedAuthData.loginTime,
        lastActivity: enhancedAuthData.lastActivity,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error("Error setting auth data:", error);
      set({
        error: "Failed to save authentication data",
        isLoading: false,
      });
    }
  },

  setSessionID: async (sessionID) => {
    try {
      await authStorage.setSessionID(sessionID);
      set({
        sessionID,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error setting session ID:", error);
      set({
        error: "Failed to save session data",
        isLoading: false,
      });
    }
  },

  clearAuthData: async () => {
    try {
      await authStorage.clearAuthData();
      await authStorage.clearSessionID();
      set({
        ...initialState,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
  },

  setError: (error) => set({ error, isLoading: false }),

  clearError: () => set({ error: null }),

  updateLastActivity: () => {
    set({ lastActivity: new Date().toISOString() });
  },

  loadAuthData: async () => {
    try {
      set({ isLoading: true });
      const authData = await authStorage.getAuthData();
      const sessionID = await authStorage.getSessionID();

      if (authData && authData.token) {
        set({
          isAuthenticated: true,
          user: authData.user || authData.username,
          token: authData.token,
          role: authData.role || "user",
          sessionID,
          loginTime: authData.loginTime,
          lastActivity: authData.lastActivity,
          isLoading: false,
          error: null,
        });
      } else if (sessionID) {
        set({
          sessionID,
          isLoading: false,
        });
      } else {
        set({ isLoading: false });
      }
    } catch (error) {
      console.error("Error loading auth data:", error);
      set({ isLoading: false });
    }
  },
}));

// Convenience hook that matches the old useAuth interface
export const useAuth = () => {
  const store = useAuthStore();

  // Load auth data on first use
  React.useEffect(() => {
    if (store.isLoading) {
      store.loadAuthData();
    }
  }, []);

  return store;
};

export default useAuthStore;
