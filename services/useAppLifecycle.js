import { useEffect, useRef } from "react";
import { AppState, Platform } from "react-native";
import { useAuthStore } from "./authStore";

/**
 * Custom hook to handle app lifecycle events and automatic logout
 */
export const useAppLifecycle = () => {
  const { clearAuthData, isAuthenticated } = useAuthStore();
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      console.log(
        "App state changed from",
        appState.current,
        "to",
        nextAppState
      );

      // Handle different app state transitions
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        // App has come to the foreground
        console.log("App has come to the foreground");
      } else if (
        appState.current === "active" &&
        nextAppState.match(/inactive|background/)
      ) {
        // App has gone to the background
        if (isAuthenticated) {
          console.log(
            "App going to background - logging out user for security"
          );
          try {
            await clearAuthData();
          } catch (error) {
            console.error("Error during background logout:", error);
          }
        }
      }

      appState.current = nextAppState;
    };

    // Subscribe to app state changes
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    // Handle app termination (component unmount)
    return () => {
      subscription?.remove();

      // Final cleanup when app is closing
      if (isAuthenticated) {
        console.log("App lifecycle cleanup - clearing auth data");
        clearAuthData().catch((error) => {
          console.error("Error during app termination logout:", error);
        });
      }
    };
  }, [isAuthenticated, clearAuthData]);

  // Additional effect for handling memory warnings and other cleanup scenarios
  useEffect(() => {
    if (Platform.OS === "ios") {
      // iOS specific memory warning handling
      const handleMemoryWarning = async () => {
        console.log("Memory warning received - clearing sensitive data");
        if (isAuthenticated) {
          try {
            await clearAuthData();
          } catch (error) {
            console.error("Error during memory warning logout:", error);
          }
        }
      };

      // Note: In a real app, you might want to use a native module to listen for memory warnings
      // For now, this is just a placeholder for the concept
    }
  }, [isAuthenticated, clearAuthData]);

  return {
    currentAppState: appState.current,
  };
};

export default useAppLifecycle;
