import { COLORS } from "@/constants/color";
import useActivityTracker from "@/services/useActivityTracker";
import useAppLifecycle from "@/services/useAppLifecycle";
import * as NavigationBar from "expo-navigation-bar";
import { Slot } from "expo-router";
import { useLayoutEffect } from "react";
import { StatusBar, StyleSheet } from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

export default function RootLayout() {
  // Initialize app lifecycle management with automatic logout
  useAppLifecycle();

  // Track user activity for session management
  useActivityTracker();
  useLayoutEffect(() => {
    NavigationBar.setButtonStyleAsync("dark");
    NavigationBar.setBackgroundColorAsync(COLORS.background);
  }, []);

  return (
    <SafeAreaProvider>
      <SafeAreaView edges={["top", "left", "right"]} style={styles.container}>
        <StatusBar
          animated={true}
          backgroundColor={COLORS.background}
          barStyle="dark-content"
        />
        <Slot />
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
});
