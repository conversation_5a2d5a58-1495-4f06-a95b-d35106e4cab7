import { COLORS } from "@/constants/color";
import { authApi } from "@/services/api";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, useLocalSearchParams, useRouter } from "expo-router";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  BackHandler,
  FlatList,
  Modal,
  RefreshControl,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

export default function DevicesScreen() {
  const router = useRouter();
  const { groupId, groupName } = useLocalSearchParams();
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);

  useEffect(() => {
    if (groupId) {
      loadDevices();
    }
  }, [groupId]);

  // Handle system back button to navigate to groups screen
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        router.push("/(tabs)/groups");
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        "hardwareBackPress",
        onBackPress
      );

      return () => subscription?.remove();
    }, [router])
  );

  const loadDevices = async () => {
    try {
      setLoading(true);
      const data = await authApi.getDevicesByGroup(groupId);
      setDevices(data || []);
    } catch (error) {
      console.error("Error loading devices:", error);
      Alert.alert("Error", "Failed to load devices. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDevices();
    setRefreshing(false);
  };

  // Filter devices based on search query
  const filteredDevices = useMemo(() => {
    if (!searchQuery.trim()) {
      return devices;
    }

    const query = searchQuery.toLowerCase();
    return devices.filter(
      (device) =>
        (device.hostname && device.hostname.toLowerCase().includes(query)) ||
        (device.ipaddress && device.ipaddress.toLowerCase().includes(query)) ||
        (device.mac && device.mac.toLowerCase().includes(query)) ||
        (device.modelname && device.modelname.toLowerCase().includes(query)) ||
        (device.gwdModelName &&
          device.gwdModelName.toLowerCase().includes(query)) ||
        (device.gateway && device.gateway.toLowerCase().includes(query))
    );
  }, [devices, searchQuery]);

  const toggleSearch = () => {
    setShowSearch(!showSearch);
    if (showSearch) {
      setSearchQuery("");
    }
  };

  const openDeviceDetails = (device) => {
    setSelectedDevice(device);
    setModalVisible(true);
  };

  const closeDeviceDetails = () => {
    setSelectedDevice(null);
    setModalVisible(false);
  };

  const getDeviceIcon = (device) => {
    if (device.modelname?.toLowerCase().includes("switch"))
      return "git-network-outline";
    if (device.modelname?.toLowerCase().includes("router"))
      return "router-outline";
    if (device.modelname?.toLowerCase().includes("ap")) return "wifi-outline";
    return "hardware-chip-outline";
  };

  const renderDeviceItem = ({ item }) => (
    <TouchableOpacity
      style={styles.deviceCard}
      activeOpacity={0.7}
      onPress={() => openDeviceDetails(item)}
    >
      <View style={styles.deviceHeader}>
        <View style={styles.deviceIcon}>
          <Ionicons
            name={getDeviceIcon(item)}
            size={24}
            color={COLORS.primary}
          />
        </View>
        <View style={styles.deviceInfo}>
          <Text style={styles.deviceName}>
            {item.hostname || "Unknown Device"}
          </Text>
          <Text style={styles.deviceSubtitle}>
            {item.ipaddress || "No IP Address"}
          </Text>
          <Text style={styles.deviceModel}>
            {item.modelname || item.gwdModelName || "Unknown Model"}
          </Text>
        </View>
        <View style={styles.statusIndicator}>
          <View
            style={[
              styles.statusDot,
              {
                backgroundColor:
                  item.status === ""
                    ? "#888"
                    : item.status === "online"
                    ? "#4CAF50"
                    : "#F44336",
              },
            ]}
          />
          <Text style={styles.statusText}>
            {item.status === ""
              ? "--"
              : item.status === "online"
              ? "Online"
              : "Offline"}
          </Text>
        </View>
      </View>

      <View style={styles.deviceQuickInfo}>
        <View style={styles.quickInfoItem}>
          <Ionicons name="finger-print" size={14} color={COLORS.textLight} />
          <Text style={styles.quickInfoText}>{item.mac}</Text>
        </View>
        {item.gateway && (
          <View style={styles.quickInfoItem}>
            <Ionicons name="globe-outline" size={14} color={COLORS.textLight} />
            <Text style={styles.quickInfoText}>{item.gateway}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading devices...</Text>
      </View>
    );
  }

  const renderDeviceDetails = () => (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={closeDeviceDetails}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {selectedDevice?.hostname || "Device Details"}
            </Text>
            <TouchableOpacity onPress={closeDeviceDetails}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            {selectedDevice && (
              <>
                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>Basic Information</Text>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Hostname:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.hostname}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>IP Address:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.ipaddress}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>MAC Address:</Text>
                    <Text style={styles.detailValue}>{selectedDevice.mac}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Status:</Text>
                    <Text
                      style={[
                        styles.detailValue,
                        {
                          color:
                            selectedDevice.status === ""
                              ? "#888"
                              : selectedDevice.status === "online"
                              ? "#4CAF50"
                              : "#F44336",
                        },
                      ]}
                    >
                      {selectedDevice.status === ""
                        ? "--"
                        : selectedDevice.status === "online"
                        ? "Online"
                        : "Offline"}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>Device Information</Text>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Model:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.modelname || selectedDevice.gwdModelName}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Application:</Text>
                    <Text style={styles.detailValue}>{selectedDevice.ap}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Kernel:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.kernel}
                    </Text>
                  </View>
                  {selectedDevice.agentVersion && (
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Agent Version:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDevice.agentVersion}
                      </Text>
                    </View>
                  )}
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Scan Protocol:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.scanproto}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Discovery Via:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.svcdiscovia}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>Network Configuration</Text>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Gateway:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.gateway}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Netmask:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.netmask}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>DHCP:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.isdhcp ? "Enabled" : "Disabled"}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Topology Protocol:</Text>
                    <Text style={styles.detailValue}>
                      {selectedDevice.topologyproto}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>SNMP Configuration</Text>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>SNMP Supported:</Text>
                    <Text
                      style={[
                        styles.detailValue,
                        {
                          color:
                            selectedDevice.snmpSupported === "1"
                              ? "#4CAF50"
                              : "#F44336",
                        },
                      ]}
                    >
                      {selectedDevice.snmpSupported === "1" ? "Yes" : "No"}
                    </Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>SNMP Enabled:</Text>
                    <Text
                      style={[
                        styles.detailValue,
                        {
                          color:
                            selectedDevice.snmpEnabled === "1"
                              ? "#4CAF50"
                              : "#F44336",
                        },
                      ]}
                    >
                      {selectedDevice.snmpEnabled === "1" ? "Yes" : "No"}
                    </Text>
                  </View>
                </View>

                {selectedDevice.syslogSetting &&
                  Object.keys(selectedDevice.syslogSetting).length > 0 && (
                    <View style={styles.detailSection}>
                      <Text style={styles.sectionTitle}>
                        Syslog Configuration
                      </Text>
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Log to Server:</Text>
                        <Text style={styles.detailValue}>
                          {selectedDevice.syslogSetting.logToServer === "1"
                            ? "Enabled"
                            : "Disabled"}
                        </Text>
                      </View>
                      {selectedDevice.syslogSetting.serverIp && (
                        <View style={styles.detailRow}>
                          <Text style={styles.detailLabel}>Server IP:</Text>
                          <Text style={styles.detailValue}>
                            {selectedDevice.syslogSetting.serverIp}
                          </Text>
                        </View>
                      )}
                      {selectedDevice.syslogSetting.serverPort && (
                        <View style={styles.detailRow}>
                          <Text style={styles.detailLabel}>Server Port:</Text>
                          <Text style={styles.detailValue}>
                            {selectedDevice.syslogSetting.serverPort}
                          </Text>
                        </View>
                      )}
                      {selectedDevice.syslogSetting.logLevel && (
                        <View style={styles.detailRow}>
                          <Text style={styles.detailLabel}>Log Level:</Text>
                          <Text style={styles.detailValue}>
                            {selectedDevice.syslogSetting.logLevel}
                          </Text>
                        </View>
                      )}
                    </View>
                  )}

                {selectedDevice.trapSetting &&
                  selectedDevice.trapSetting.length > 0 && (
                    <View style={styles.detailSection}>
                      <Text style={styles.sectionTitle}>
                        SNMP Trap Settings
                      </Text>
                      {selectedDevice.trapSetting.map((trap, index) => (
                        <View key={index} style={styles.trapContainer}>
                          <Text style={styles.trapTitle}>
                            Trap Server {index + 1}
                          </Text>
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>Server IP:</Text>
                            <Text style={styles.detailValue}>
                              {trap.serverIp}
                            </Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>Port:</Text>
                            <Text style={styles.detailValue}>
                              {trap.serverPort}
                            </Text>
                          </View>
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>Community:</Text>
                            <Text style={styles.detailValue}>
                              {trap.community}
                            </Text>
                          </View>
                        </View>
                      ))}
                    </View>
                  )}

                {selectedDevice.gpsInfo && (
                  <View style={styles.detailSection}>
                    <Text style={styles.sectionTitle}>GPS Information</Text>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>GPS Enabled:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDevice.gpsInfo.enabled === "1" ? "Yes" : "No"}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Last Update:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDevice.gpsInfo.lasttime || "N/A"}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Latitude:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDevice.gpsInfo.latitude || "N/A"}
                      </Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Longitude:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDevice.gpsInfo.longitude || "N/A"}
                      </Text>
                    </View>
                  </View>
                )}

                <View style={styles.detailSection}>
                  <Text style={styles.sectionTitle}>Capabilities</Text>
                  <View style={styles.capabilitiesContainer}>
                    {selectedDevice.supported?.map((capability, index) => (
                      <View key={index} style={styles.capabilityChip}>
                        <Text style={styles.capabilityText}>{capability}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              </>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push("/(tabs)/groups")}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.title}>{groupName}</Text>
            <Text style={styles.subtitle}>
              {filteredDevices.length} of {devices.length} device
              {devices.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <TouchableOpacity style={styles.searchButton} onPress={toggleSearch}>
            <Ionicons
              name={showSearch ? "close" : "search"}
              size={20}
              color={COLORS.text}
            />
          </TouchableOpacity>
        </View>

        {showSearch && (
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color={COLORS.textLight} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search devices..."
              placeholderTextColor={COLORS.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus={true}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={COLORS.textLight}
                />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      <FlatList
        data={filteredDevices}
        renderItem={renderDeviceItem}
        keyExtractor={(item, index) => item.mac || index.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons
              name={searchQuery ? "search" : "cube-outline"}
              size={64}
              color={COLORS.textLight}
            />
            <Text style={styles.emptyTitle}>
              {searchQuery ? "No Devices Found" : "No Devices"}
            </Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery
                ? `No devices match "${searchQuery}"`
                : "This group doesn't have any devices yet."}
            </Text>
          </View>
        )}
      />

      {renderDeviceDetails()}
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.textLight,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 16,
  },
  headerTop: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.card,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  searchButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.card,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.card,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 12,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textLight,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  deviceCard: {
    backgroundColor: COLORS.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  deviceHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  deviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${COLORS.primary}15`,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: "600",
    color: COLORS.text,
    marginBottom: 2,
  },
  deviceSubtitle: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 2,
  },
  deviceModel: {
    fontSize: 12,
    color: COLORS.textLight,
    fontStyle: "italic",
  },
  statusIndicator: {
    alignItems: "center",
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 10,
    color: COLORS.textLight,
    fontWeight: "500",
  },
  deviceQuickInfo: {
    flexDirection: "row",
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  quickInfoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  quickInfoText: {
    fontSize: 12,
    color: COLORS.textLight,
    marginLeft: 4,
  },
  deviceDetails: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: COLORS.textLight,
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: COLORS.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: COLORS.textLight,
    textAlign: "center",
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: COLORS.text,
  },
  modalBody: {
    padding: 20,
  },
  detailSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: COLORS.text,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.textLight,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: "500",
    flex: 1,
    textAlign: "right",
  },
  capabilitiesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  capabilityChip: {
    backgroundColor: `${COLORS.primary}15`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  capabilityText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: "500",
  },
  trapContainer: {
    backgroundColor: `${COLORS.primary}08`,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.primary,
  },
  trapTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: COLORS.text,
    marginBottom: 8,
  },
};
