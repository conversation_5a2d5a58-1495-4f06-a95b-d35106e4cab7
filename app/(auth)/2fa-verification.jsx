import { COLORS } from "@/constants/color";
import { useAuth } from "@/services/authStore";
import useAuthOperations from "@/services/useAuth";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";

const TwoFAVerificationScreen = () => {
  const router = useRouter();
  const { sessionID, clearAuthData } = useAuth();
  const { validate2FA, isLoading } = useAuthOperations();

  const [code, setCode] = useState("");
  const [error, setError] = useState("");

  useEffect(() => {
    // If no session ID, redirect to sign-in
    if (!sessionID) {
      router.replace("/(auth)/sign-in");
    }
  }, [sessionID]);

  const handleVerify2FA = async () => {
    if (!code.trim()) {
      setError("Please enter the 2FA code");
      return;
    }

    if (code.length !== 6) {
      setError("2FA code must be 6 digits");
      return;
    }

    setError("");

    try {
      await validate2FA(sessionID, code.trim());
      // Navigate to main app on success
      router.replace("/(tabs)");
    } catch (error) {
      console.error("2FA verification error:", error);
      setError(error.message || "Invalid 2FA code. Please try again.");
    }
  };

  const handleCancel = async () => {
    Alert.alert(
      "Cancel Authentication",
      "Are you sure you want to cancel? You'll need to sign in again.",
      [
        {
          text: "No",
          style: "cancel",
        },
        {
          text: "Yes",
          style: "destructive",
          onPress: async () => {
            await clearAuthData();
            router.replace("/(auth)/sign-in");
          },
        },
      ]
    );
  };

  const formatCode = (text) => {
    // Remove non-numeric characters and limit to 6 digits
    const numericText = text.replace(/[^0-9]/g, "").slice(0, 6);
    return numericText;
  };

  return (
    <KeyboardAvoidingView
      behavior="padding"
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 40}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.imageContainer}>
            <Image
              source={require("@/assets/images/2fa.png")}
              style={styles.image}
            />
          </View>

          <Text style={styles.title}>Two-Factor Authentication</Text>
          <Text style={styles.subtitle}>
            Open Google Authenticator to get your 2FA code
          </Text>

          {/* FORM CONTAINER */}
          <View style={styles.formContainer}>
            {/* 2FA Code Input */}
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.textInput,
                  { textAlign: "center", fontSize: 18, letterSpacing: 2 },
                ]}
                placeholder="000000"
                placeholderTextColor={COLORS.textLight}
                value={code}
                onChangeText={(text) => setCode(formatCode(text))}
                keyboardType="numeric"
                maxLength={6}
                autoFocus
              />
            </View>

            {error ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={16} color={COLORS.error} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            <TouchableOpacity
              style={[
                styles.authButton,
                (isLoading || code.length !== 6) && styles.buttonDisabled,
              ]}
              onPress={handleVerify2FA}
              disabled={isLoading || code.length !== 6}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <ActivityIndicator color={COLORS.white} size="small" />
              ) : (
                <Text style={styles.buttonText}>Verify Code</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.authButton, styles.secondaryButton]}
              onPress={handleCancel}
              disabled={isLoading}
              activeOpacity={0.8}
            >
              <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default TwoFAVerificationScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
  },
  imageContainer: {
    alignItems: "center",
    marginVertical: 20,
  },
  image: {
    width: 200,
    height: 200,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.text,
    textAlign: "center",
    marginVertical: 10,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: "center",
    marginBottom: 20,
  },
  formContainer: {
    marginHorizontal: 20,
  },
  inputContainer: {
    marginBottom: 10,
  },
  textInput: {
    fontSize: 18,
    color: COLORS.text,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.textLight,
    paddingVertical: 5,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  errorText: {
    fontSize: 14,
    color: COLORS.error,
    marginLeft: 5,
  },
  authButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonText: {
    fontSize: 16,
    color: COLORS.white,
    textAlign: "center",
  },
  secondaryButton: {
    backgroundColor: COLORS.textLight,
  },
  secondaryButtonText: {
    color: COLORS.text,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
});
