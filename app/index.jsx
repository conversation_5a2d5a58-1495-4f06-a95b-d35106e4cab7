import useAuthStore from "@/services/authStore";
import { Redirect } from "expo-router";
import { useEffect } from "react";

export default function Index() {
  const { isAuthenticated, token, sessionID, isLoading, loadAuthData } =
    useAuthStore();

  // Load auth data when component mounts
  useEffect(() => {
    loadAuthData();
  }, []);

  // Show loading state while checking authentication
  if (isLoading) {
    return null; // You can replace this with a loading spinner component
  }

  // If user is fully authenticated, redirect to main app
  if (isAuthenticated && token) {
    return <Redirect href="/(tabs)" />;
  }

  // If user has a session ID (2FA pending), redirect to 2FA verification
  if (sessionID) {
    return <Redirect href="/(auth)/2fa-verification" />;
  }

  // Otherwise, redirect to sign-in
  return <Redirect href="/(auth)/sign-in" />;
}
